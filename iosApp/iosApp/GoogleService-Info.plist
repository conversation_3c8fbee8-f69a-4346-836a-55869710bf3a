<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN"
    "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>CLIENT_ID</key>
        <string>190961714567-4s15ukcsnadgv9cua66ffuboa089c3r3.apps.googleusercontent.com</string>
        <key>REVERSED_CLIENT_ID</key>
        <string>com.googleusercontent.apps.190961714567-4s15ukcsnadgv9cua66ffuboa089c3r3</string>
        <key>ANDROID_CLIENT_ID</key>
        <string>190961714567-upsa2kom8asgj4snie7kctkkef08g0lj.apps.googleusercontent.com</string>
        <key>API_KEY</key>
        <string>AIzaSyD0FuKJkETfzVyWI6tj8s3xB8KKT1VlBQU</string>
        <key>GCM_SENDER_ID</key>
        <string>190961714567</string>
        <key>PLIST_VERSION</key>
        <string>1</string>
        <key>BUNDLE_ID</key>
        <string>com.zrcoding.barbcker</string>
        <key>PROJECT_ID</key>
        <string>barbcker</string>
        <key>STORAGE_BUCKET</key>
        <string>barbcker.firebasestorage.app</string>
        <key>IS_ADS_ENABLED</key>
        <false></false>
        <key>IS_ANALYTICS_ENABLED</key>
        <false></false>
        <key>IS_APPINVITE_ENABLED</key>
        <true></true>
        <key>IS_GCM_ENABLED</key>
        <true></true>
        <key>IS_SIGNIN_ENABLED</key>
        <true></true>
        <key>GOOGLE_APP_ID</key>
        <string>1:190961714567:ios:c7ce20ac0f3ed29dcc7f22</string>
    </dict>
</plist>