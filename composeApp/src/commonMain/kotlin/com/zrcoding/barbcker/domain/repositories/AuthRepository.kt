package com.zrcoding.barbcker.domain.repositories

import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.AuthStatus
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.Resource

interface AuthRepository {
    suspend fun authenticateWithGoogle(): Resource<AuthStatus, AuthErrors>

    suspend fun authenticateWithApple(): Resource<AuthStatus, AuthErrors>

    suspend fun completeAccount(
        name: String,
        currency: Currency
    ): Resource<Unit, AuthErrors>
}