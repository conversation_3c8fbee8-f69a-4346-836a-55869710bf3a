package com.zrcoding.barbcker.domain.repositories

import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import kotlinx.coroutines.flow.Flow

interface AccountRepository {
    suspend fun saveAccount(entrant: Entrant, shopName: String? = null, currency: Currency? = null)

    suspend fun editAccount(shopName: String, currency: Currency)

    suspend fun getAccount(): Flow<Account>
}