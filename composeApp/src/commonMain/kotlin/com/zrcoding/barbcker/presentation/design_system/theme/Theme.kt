package com.zrcoding.barbcker.presentation.design_system.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable

private val DarkColorPalette = darkColorScheme(
    primary = purple900,
    onPrimary = White600,
    secondary = purple100,
    background = Black900,
    onBackground = White600,
    error = Red800,
    surfaceContainer = Black700,
    surfaceContainerHighest = Black700,
)

private val LightColorPalette = lightColorScheme(
    primary = purple900,
    onPrimary = White,
    secondary = purple100,
    background = White,
    onBackground = Black900,
    error = Red800,
    surfaceContainer = purple100,
    surfaceContainerHighest = White600,
)

@Composable
fun BarbckerTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit) {
    val colors = if (darkTheme) {
        DarkColorPalette
    } else {
        LightColorPalette
    }

    MaterialTheme(
        colorScheme = colors,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}

val MaterialTheme.dimension: Dimens
    get() = Dimens()