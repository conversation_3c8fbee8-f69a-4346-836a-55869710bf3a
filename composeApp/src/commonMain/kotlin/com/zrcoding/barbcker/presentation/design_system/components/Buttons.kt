package com.zrcoding.barbcker.presentation.design_system.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Black
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.White
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
fun BcPrimaryButton(
    modifier: Modifier = Modifier,
    text: String,
    leadingIcon: @Composable () -> Unit = {},
    trailingIcon: @Composable () -> Unit = {},
    colors: ButtonColors = ButtonDefaults.buttonColors(
        disabledContainerColor = MaterialTheme.colorScheme.secondary,
    ),
    enabled: Boolean = true,
    loading: Boolean = false,
    onClick: () -> Unit,
) {
    Button(
        modifier = modifier,
        shape = CircleShape,
        colors = colors,
        onClick = onClick,
        enabled = enabled && loading.not(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
        ) {
            if (loading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    trackColor = MaterialTheme.colorScheme.background,
                )
            }
            leadingIcon()
            Text(
                style = MaterialTheme.typography.titleSmall,
                text = text,
            )
            trailingIcon()
        }
    }
}

@Preview
@Composable
private fun BcPrimaryButtonPreview() {
    BarbckerTheme {
        Column {
            BcPrimaryButton(
                text = "Enabled Button",
                onClick = { }
            )
            BcPrimaryButton(
                text = "Disabled Button",
                enabled = false,
                onClick = { }
            )
            BcPrimaryButton(
                text = "Loading Button",
                loading = true,
                onClick = { }
            )
        }
    }
}

@Composable
fun BcSecondaryButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean = true,
    leadingIcon: @Composable () -> Unit = {},
    trailingIcon: @Composable () -> Unit = {},
    colors: ButtonColors = ButtonDefaults.buttonColors(
        containerColor = White,
        contentColor = Black
    ),
    onClick: () -> Unit,
) {
    OutlinedButton(
        modifier = modifier,
        shape = CircleShape,
        enabled = enabled,
        colors = colors,
        onClick = onClick
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
        ) {
            leadingIcon()
            Text(
                style = MaterialTheme.typography.titleSmall,
                text = text,
            )
            trailingIcon()
        }
    }
}

@Preview
@Composable
private fun BcSecondaryButtonPreview() {
    BarbckerTheme {
        Column {
            BcSecondaryButton(
                text = "Button text",
                onClick = { }
            )
        }
    }
}

@Composable
fun BcIconButton(
    modifier: Modifier = Modifier,
    icon: DrawableResource,
    colors: IconButtonColors = IconButtonDefaults.filledIconButtonColors(),
    onClick: () -> Unit
) {
    FilledIconButton(
        modifier = modifier,
        colors = colors,
        onClick = onClick
    ) {
        Icon(
            painter = painterResource(icon),
            contentDescription = null
        )
    }
}

@Composable
fun BcIconButton(
    modifier: Modifier = Modifier,
    icon: ImageVector,
    colors: IconButtonColors = IconButtonDefaults.filledIconButtonColors(),
    onClick: () -> Unit
) {
    FilledIconButton(
        modifier = modifier,
        colors = colors,
        onClick = onClick
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null
        )
    }
}