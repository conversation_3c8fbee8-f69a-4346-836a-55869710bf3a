package com.zrcoding.barbcker.presentation.features.auth

import androidx.compose.runtime.Stable

@Stable
data class AuthViewState(
    val services: List<AuthService> = availableAuthServices,
)

enum class AuthService {
    GOOGLE,
    APPLE
}

expect val availableAuthServices: List<AuthService>

sealed interface AuthOneTimeEvents {
    data object NavigateToHome : AuthOneTimeEvents
    data object NavigateToCompleteAccount : AuthOneTimeEvents
}