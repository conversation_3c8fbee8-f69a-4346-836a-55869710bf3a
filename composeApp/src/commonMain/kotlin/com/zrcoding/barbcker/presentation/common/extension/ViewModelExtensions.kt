package com.zrcoding.barbcker.presentation.common.extension

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_network_error_verify_and_try_later
import barbcker.composeapp.generated.resources.common_unexpected_error_verify_and_try_later
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarAction
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarController
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarEvent
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarType
import com.zrcoding.barbcker.presentation.design_system.utils.UiText
import kotlinx.coroutines.launch

/**
 * Displays a success message using a SnackBar.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays the provided
 * success message. The SnackBar is configured to include a dismiss action for user convenience.
 *
 * @param message The message to be displayed, represented as a [UiText].
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderSuccess(message: UiText) {
    viewModelScope.launch {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.SUCCESS,
                withDismissAction = true
            )
        )
    }
}

/**
 * Displays a success message using a SnackBar.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays the provided
 * success message. The SnackBar is configured to include a dismiss action for user convenience.
 *
 * @param message The message to be displayed, represented as a [UiText].
 * @property action A [SnackBarAction] that provides an action button on the snackbar, such as an "Undo" button.
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderSuccess(message: UiText, action: SnackBarAction) {
    viewModelScope.launch {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.SUCCESS,
                withDismissAction = false,
                action = action
            )
        )
    }
}

/**
 * Displays an error message using a SnackBar.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays an appropriate
 * error message.
 * The SnackBar is configured to include a dismiss action for user convenience.
 *
 * @param message The error to be handled and displayed.
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderFailure(message: UiText) {
    viewModelScope.launch {
        SnackBarController.showSnackBar(
            event = SnackBarEvent(
                message = message,
                type = SnackBarType.ERROR,
                withDismissAction = true
            )
        )
    }
}

/**
 * Displays an error message using a SnackBar based on a network or backend error.
 *
 * This function uses [SnackBarController] to emit a [SnackBarEvent] that displays an appropriate
 * error message based on the provided [AuthErrors.Network].
 * The SnackBar is configured to include a dismiss action for user convenience.
 *
 * - For network errors, a predefined message resource is resolved based on the type of error (e.g., no internet or unknown error).
 * - For backend errors, the error message is displayed as plain text.
 *
 * @param error The error to be handled and displayed, represented as a [AuthErrors.Network].
 *
 * @see SnackBarController
 * @see SnackBarEvent
 */
fun ViewModel.renderFailure(error: AuthErrors.Network) {
    renderFailure(error.asUiText())
}

/**
 * Converts a [AuthErrors.Network] instance into a [UiText] representation.
 *
 * This function maps different network and backend error cases to user-facing UI text,
 * either as a string resource ID wrapped in [UiText.FromRes] or as raw text in [UiText.Text].
 *
 * - For network errors ([AuthErrors.Network]), specific error cases are mapped
 *   to string resource IDs.
 *
 * @receiver A [AuthErrors.Network] instance to be converted.
 * @return A [UiText] instance representing the error in a format suitable for the UI.
 */
fun AuthErrors.Network.asUiText(): UiText = when (error) {
    NetworkErrors.NO_INTERNET -> Res.string.common_network_error_verify_and_try_later
    NetworkErrors.UNKNOWN -> Res.string.common_unexpected_error_verify_and_try_later
}.let { UiText.FromRes(it) }