package com.zrcoding.barbcker.presentation.design_system.utils

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarData
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import com.zrcoding.barbcker.presentation.common.extension.ObserveAsEvents
import com.zrcoding.barbcker.presentation.design_system.theme.Brown400
import com.zrcoding.barbcker.presentation.design_system.theme.Brown500
import com.zrcoding.barbcker.presentation.design_system.theme.Green700
import com.zrcoding.barbcker.presentation.design_system.theme.Red700
import com.zrcoding.barbcker.presentation.design_system.theme.White
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarController.events
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Represents different types of snackbars with associated background color.
 *
 * @property backgroundColor The background color of the snackbar.
 */
enum class SnackBarType(
    val backgroundColor: Color,
) {
    SUCCESS(backgroundColor = Green700),
    ERROR(backgroundColor = Red700),
    WARNING(backgroundColor = Brown500);

    companion object {
        /**
         * Determines the [SnackBarType] based on the success state.
         *
         * @param isSuccess A boolean indicating if the result is successful (`true`) or an error (`false`).
         * @return [SnackBarType.SUCCESS] if [isSuccess] is `true`, otherwise [SnackBarType.ERROR].
         */
        fun successOrError(isSuccess: Boolean): SnackBarType {
            return if (isSuccess) SUCCESS else ERROR
        }
    }
}

/**
 * Represents a snackbar event to be displayed in the UI.
 *
 * @property message The main message content of the snackbar, represented as [UiText].
 * @property type The [SnackBarType].
 * @property action An optional [SnackBarAction] that provides an action button on the snackbar, such as an "Undo" button.
 */
data class SnackBarEvent(
    val message: UiText,
    val type: SnackBarType,
    val withDismissAction: Boolean = false,
    val duration: SnackbarDuration = SnackbarDuration.Short,
    val action: SnackBarAction? = null,
)

/**
 * Represents an action button for a snackbar.
 *
 * @property name The text to display on the action button, represented as [UiText].
 * @property onClicked A lambda function to be executed when the action button is clicked.
 */
data class SnackBarAction(
    val name: UiText,
    val onClicked: () -> Unit
)

/**
 * Controls the display of snackbar events, providing an interface to send and observe snackbar events.
 *
 * The controller uses a [Channel] to manage the queue of [SnackBarEvent] instances, which can be observed
 * as a flow of events via the [events] property.
 */
object SnackBarController {
    private val _events = Channel<SnackBarEvent>()
    val events = _events.receiveAsFlow()

    /**
     * Displays a snackbar by emitting a [SnackBarEvent] to the event flow. Can be called everywhere
     * from ui or viewModel.
     *
     * @param event The [SnackBarEvent] containing the message, colors, and optional action for the snackbar.
     */
    suspend fun showSnackBar(event: SnackBarEvent) {
        _events.send(event)
    }
}

/**
 * A composable function that displays a `SnackbarHost` which observes and shows events emitted by `SnackBarController`.
 *
 * This composable listens to events from `SnackBarController` and displays corresponding snackbars with customized
 * background colors, messages, actions, and durations based on the event details. It dynamically updates the snackbar's
 * appearance and behavior according to each received event, providing reactive feedback for success and error states.
 *
 * @param modifier [Modifier] applied to the `SnackbarHost` for customization.
 *
 * Example usage:
 * ```
 *  Scaffold(
 *  *     snackbarHost = { SnackBarHostForSnackBarController(modifier = Modifier.padding(16.dp)) }
 *  * ) { innerPadding ->
 *  *     // Content goes here
 *  * }
 * ```
 */
@Composable
fun SnackBarHostForSnackBarController(
    modifier: Modifier = Modifier
) {
    val scope = rememberCoroutineScope()
    val snackBarHostState = remember { SnackbarHostState() }
    var snackBarBgColor by remember { mutableStateOf(SnackBarType.SUCCESS.backgroundColor) }

    ObserveAsEvents(
        flow = SnackBarController.events,
        key1 = snackBarHostState
    ) { event ->
        scope.launch {
            snackBarHostState.currentSnackbarData?.dismiss()
            snackBarBgColor = event.type.backgroundColor
            val result = snackBarHostState.showSnackbar(
                message = event.message.getValue(),
                actionLabel = event.action?.name?.getValue(),
                withDismissAction = event.withDismissAction,
                duration = event.duration,
            )
            if (result == SnackbarResult.ActionPerformed) {
                event.action?.onClicked?.invoke()
            }
        }
    }
    snackBarHostState.currentSnackbarData?.let { snackBarData ->
        SnackbarHost(
            modifier = modifier.padding(MaterialTheme.dimension.large),
            hostState = snackBarHostState
        ) {
            SnackBarWithCountDown(
                snackBarData = snackBarData,
                snackBarBgColor = snackBarBgColor
            )
        }
    }
}

private fun SnackbarDuration.toMillis(): Int = when (this) {
    SnackbarDuration.Indefinite -> Int.MAX_VALUE
    // In the docs => SnackbarDuration.Short -> 10000L
    SnackbarDuration.Long -> 10000
    // In the docs => SnackbarDuration.Short -> 4000L
    SnackbarDuration.Short -> 4000
}

@Composable
fun SnackBarWithCountDown(
    modifier: Modifier = Modifier,
    snackBarData: SnackbarData,
    snackBarBgColor: Color,
) {
    val visuals = snackBarData.visuals
    val actionLabel = snackBarData.visuals.actionLabel
    val actionComposable: (@Composable () -> Unit)? = if (actionLabel != null) {
        @Composable {
            TextButton(
                colors = ButtonDefaults.textButtonColors(contentColor = Brown400),
                onClick = { snackBarData.performAction() },
                content = { Text(actionLabel) }
            )
        }
    } else {
        null
    }
    val dismissActionComposable: (@Composable () -> Unit)? = if (visuals.withDismissAction) {
        @Composable {
            IconButton(
                onClick = { snackBarData.dismiss() },
                content = {
                    Icon(
                        Icons.Filled.Close,
                        contentDescription = "Dismiss snackBar",
                    )
                }
            )
        }
    } else {
        null
    }
    Snackbar(
        modifier = modifier,
        containerColor = snackBarBgColor,
        action = actionComposable,
        dismissAction = dismissActionComposable
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
        ) {
            // Show Countdown only for Short and Long durations.
            if (visuals.duration != SnackbarDuration.Indefinite) {
                val totalDurationMills = visuals.duration.toMillis()
                var millisRemaining by remember { mutableIntStateOf(totalDurationMills) }
                LaunchedEffect(visuals) {
                    while (millisRemaining > 0) {
                        delay(40)
                        millisRemaining -= 40
                    }
                }
                SnackBarCountdown(
                    // Calculate the progress of the timer
                    timerProgress = millisRemaining.toFloat() / totalDurationMills.toFloat(),
                    // Calculate the remaining seconds
                    secondsRemaining = (millisRemaining / 1000) + 1,
                    color = White
                )
            }
            // Display the message
            Text(
                text = visuals.message,
                color = White
            )
        }
    }
}

@Composable
fun SnackBarCountdown(
    timerProgress: Float,
    secondsRemaining: Int,
    color: Color
) {
    Box(
        modifier = Modifier.size(MaterialTheme.dimension.extraBig),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .padding(MaterialTheme.dimension.small)
                .matchParentSize()
        ) {
            // Define the stroke
            val strokeStyle = Stroke(
                width = 3.dp.toPx(),
                cap = StrokeCap.Round
            )
            // Draw the track
            drawCircle(
                color = color.copy(alpha = 0.12f),
                style = strokeStyle
            )
            // Draw the progress
            drawArc(
                color = color,
                startAngle = -90f,
                sweepAngle = (-360f * timerProgress),
                useCenter = false,
                style = strokeStyle
            )
        }
        // Display the remaining seconds
        Text(
            text = secondsRemaining.toString(),
            style = MaterialTheme.typography.bodyMedium,
            color = color
        )
    }
}

