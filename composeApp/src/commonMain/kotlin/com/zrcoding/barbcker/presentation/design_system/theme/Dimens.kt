package com.zrcoding.barbcker.presentation.design_system.theme

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

data class Dimens(
    val none: Dp = 0.dp,
    val tiny: Dp = 2.dp,
    val small: Dp = 4.dp,
    val medium: Dp = 8.dp,
    val large: Dp = 12.dp,
    val default: Dp = 16.dp,
    val big: Dp = 20.dp,
    val bigger: Dp = 24.dp,
    val extraBig: Dp = 40.dp,
    val screenPaddingHorizontal: Dp = 20.dp
)
