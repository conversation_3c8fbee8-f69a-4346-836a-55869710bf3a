package com.zrcoding.barbcker.presentation.design_system.theme

import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.roboto_medium
import barbcker.composeapp.generated.resources.roboto_regular
import org.jetbrains.compose.resources.Font

// Set of Material typography styles to start with
val Typography: Typography
    @Composable
    get() = Typography(
        displayLarge = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 57.sp,
            lineHeight = 64.sp,
            letterSpacing = (-0.25).sp,
        ),
        displayMedium = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 45.sp,
            lineHeight = 52.sp,
            letterSpacing = 0.sp,
        ),
        displaySmall = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 36.sp,
            lineHeight = 44.sp,
            letterSpacing = 0.sp,
        ),
        headlineLarge = TextStyle(
            fontWeight = FontWeight.Bold,
            fontFamily = FontFamily(Font(Res.font.roboto_medium)),
            fontSize = 32.sp,
            lineHeight = 40.sp,
            letterSpacing = 0.sp,
        ),
        headlineMedium = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_medium)),
            fontSize = 28.sp,
            lineHeight = 36.sp,
            letterSpacing = 0.sp,
        ),
        headlineSmall = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 24.sp,
            lineHeight = 32.sp,
            letterSpacing = 0.sp,
        ),
        titleLarge = TextStyle(
            fontWeight = FontWeight.Medium,
            fontFamily = FontFamily(Font(Res.font.roboto_medium)),
            fontSize = 22.sp,
            lineHeight = 28.sp,
            letterSpacing = 0.sp,
        ),
        titleMedium = TextStyle(
            fontWeight = FontWeight.Medium,
            fontFamily = FontFamily(Font(Res.font.roboto_medium)),
            fontSize = 18.sp,
            lineHeight = 24.sp,
            letterSpacing = 0.1.sp,
        ),
        titleSmall = TextStyle(
            fontWeight = FontWeight.Medium,
            fontFamily = FontFamily(Font(Res.font.roboto_medium)),
            fontSize = 14.sp,
            lineHeight = 20.sp,
            letterSpacing = 0.1.sp,
        ),
        bodyLarge = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 16.sp,
            lineHeight = 24.sp,
            letterSpacing = 0.5.sp,
        ),
        bodyMedium = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 14.sp,
            lineHeight = 20.sp,
            letterSpacing = 0.25.sp,
        ),
        bodySmall = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 12.sp,
            lineHeight = 16.sp,
            letterSpacing = 0.4.sp,
        ),
        labelLarge = TextStyle(
            fontWeight = FontWeight.Medium,
            fontFamily = FontFamily(Font(Res.font.roboto_medium)),
            fontSize = 14.sp,
            lineHeight = 20.sp,
            letterSpacing = 0.1.sp,
        ),
        labelMedium = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 12.sp,
            lineHeight = 16.sp,
            letterSpacing = 0.5.sp,
        ),
        labelSmall = TextStyle(
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily(Font(Res.font.roboto_regular)),
            fontSize = 11.sp,
            lineHeight = 16.sp,
            letterSpacing = 0.sp,
        ),
    )