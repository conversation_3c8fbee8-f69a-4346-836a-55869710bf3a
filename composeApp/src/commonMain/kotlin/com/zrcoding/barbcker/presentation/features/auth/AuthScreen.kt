package com.zrcoding.barbcker.presentation.features.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.ic_apple
import barbcker.composeapp.generated.resources.ic_google
import barbcker.composeapp.generated.resources.img_screen_top_illustration
import barbcker.composeapp.generated.resources.sign_in_apple_button
import barbcker.composeapp.generated.resources.sign_in_description
import barbcker.composeapp.generated.resources.sign_in_google_button
import barbcker.composeapp.generated.resources.sign_in_title
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenDescription
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenTitle
import com.zrcoding.barbcker.presentation.design_system.components.BcSecondaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun AuthRoute(
    navigateBack: () -> Unit,
    navigateToCompleteAccount: () -> Unit,
    navigateToHome: () -> Unit,
    viewModel: AuthViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    Box(modifier = Modifier.fillMaxSize()) {
        AuthScreen(
            viewState = viewState,
            navigateBack = navigateBack,
            onGoogleBtnClicked = viewModel::onGoogleBtnClicked,
            onAppleBtnClicked = viewModel::onAppleBtnClicked,
            modifier = Modifier.fillMaxSize()
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                AuthOneTimeEvents.NavigateToHome -> navigateToHome()
                AuthOneTimeEvents.NavigateToCompleteAccount -> navigateToCompleteAccount()
            }
        }
    }
}

@Composable
fun BoxScope.AuthScreen(
    viewState: AuthViewState,
    navigateBack: () -> Unit,
    onGoogleBtnClicked: () -> Unit,
    onAppleBtnClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier.fillMaxWidth().aspectRatio(1f),
            painter = painterResource(Res.drawable.img_screen_top_illustration),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        Column(
            modifier = modifier
                .padding(
                    horizontal = MaterialTheme.dimension.screenPaddingHorizontal,
                    vertical = MaterialTheme.dimension.big,
                )
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BcScreenTitle(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(Res.string.sign_in_title),
                textAlign = TextAlign.Start
            )
            Spacer(modifier = Modifier.padding(MaterialTheme.dimension.small))
            BcScreenDescription(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(Res.string.sign_in_description),
                textAlign = TextAlign.Start
            )
            Spacer(modifier = Modifier.padding(MaterialTheme.dimension.large))
            if (viewState.services.contains(AuthService.GOOGLE)) {
                BcSecondaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.sign_in_google_button),
                    leadingIcon = {
                        Image(
                            modifier = Modifier.size(MaterialTheme.dimension.bigger),
                            painter = painterResource(Res.drawable.ic_google),
                            contentDescription = null
                        )
                    },
                    onClick = onGoogleBtnClicked
                )
            }
            if (viewState.services.contains(AuthService.APPLE)) {
                Spacer(modifier = Modifier.height(MaterialTheme.dimension.small))
                BcPrimaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.sign_in_apple_button),
                    leadingIcon = {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.dimension.bigger),
                            painter = painterResource(Res.drawable.ic_apple),
                            contentDescription = null
                        )
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.onBackground,
                        contentColor = MaterialTheme.colorScheme.background,
                    ),
                    onClick = onAppleBtnClicked
                )
            }
        }
    }
    BcTopAppBar(
        onNavigationIconClicked = navigateBack,
        modifier = Modifier.align(Alignment.TopCenter).statusBarsPadding()
    )
}

@Preview
@Composable
fun AuthPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier.fillMaxSize().background(MaterialTheme.colorScheme.background)
        ) {
            AuthScreen(
                viewState = AuthViewState(),
                navigateBack = {},
                onGoogleBtnClicked = {},
                onAppleBtnClicked = {}
            )
        }
    }
}