package com.zrcoding.barbcker.presentation.design_system.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.getString
import org.jetbrains.compose.resources.stringResource

@Stable
sealed interface UiText {
    @Stable
    data class FromRes(val id: StringResource, val args: List<Any> = emptyList()) : UiText

    @Stable
    data class Text(val value: String) : UiText

    @Composable
    fun getValueFromComposable(): String = when (this) {
        is Text -> value
        is FromRes -> stringResource(id, args)
    }

    suspend fun getValue(): String = when (this) {
        is Text -> value
        is FromRes -> getString(id, args)
    }
}