package com.zrcoding.barbcker.presentation.navigation

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.img_barber_working
import com.zrcoding.barbcker.presentation.features.app.StartDestination
import com.zrcoding.barbcker.presentation.features.auth.AuthRoute
import com.zrcoding.barbcker.presentation.features.complete_account.CompleteAccountRoute
import com.zrcoding.barbcker.presentation.features.onboarding.OnboardingScreen
import org.jetbrains.compose.resources.painterResource

@Composable
fun BarbckerNavHost(
    modifier: Modifier = Modifier,
    startDestination: StartDestination.Screen,
) {
    val navController = rememberNavController()
    NavHost(
        navController = navController,
        startDestination = when (startDestination) {
            is StartDestination.Screen.Auth -> Auth
            is StartDestination.Screen.Home -> Home
        },
        modifier = modifier
    ) {
        composableWithAnimation<Onboarding> {
            OnboardingScreen(
                onNextClick = {
                    navController.navigate(Auth) {
                        popUpTo(Onboarding) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        composableWithAnimation<Auth> {
            AuthRoute(
                navigateBack = { navController.popBackStack() },
                navigateToCompleteAccount = {
                    navController.navigate(CompleteAccount)
                },
                navigateToHome = {
                    navController.navigate(Home) {
                        popUpTo(Auth) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        composableWithAnimation<CompleteAccount> {
            CompleteAccountRoute(
                navigateBack = { navController.popBackStack() },
                navigateToHome = {
                    navController.navigate(Home) {
                        popUpTo(CompleteAccount) {
                            inclusive = true
                        }
                    }
                },
                navigateToAuth = {
                    navController.popBackStack()
                }
            )
        }
        composableWithAnimation<Home> {
            Image(
                painter = painterResource(Res.drawable.img_barber_working),
                contentDescription = null
            )
        }
    }
}

private inline fun <reified T : Any> NavGraphBuilder.composableWithAnimation(
    duration: Int = 400,
    noinline composable: @Composable AnimatedContentScope.(NavBackStackEntry) -> Unit
) {
    composable<T>(
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        }
    ) {
        composable(it)
    }
}