package com.zrcoding.barbcker.presentation.features.complete_account

import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.ArrowDropUp
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_can_be_changed_later
import barbcker.composeapp.generated.resources.common_continue
import barbcker.composeapp.generated.resources.complete_account_currency_label
import barbcker.composeapp.generated.resources.complete_account_description
import barbcker.composeapp.generated.resources.complete_account_shop_name_label
import barbcker.composeapp.generated.resources.complete_account_shop_name_placeholder
import barbcker.composeapp.generated.resources.complete_account_title
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTextField
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.components.bcReadOnlyTextFieldColors
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel


@Composable
fun CompleteAccountRoute(
    navigateBack: () -> Unit,
    navigateToHome: () -> Unit,
    navigateToAuth: () -> Unit,
    viewModel: CompleteAccountViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    CompleteAccountScreen(
        viewState = viewState,
        onNavigateBack = navigateBack,
        onShopNameChanged = viewModel::onShopNameChanged,
        onCurrencyChanged = viewModel::onCurrencyChanged,
        onSubmit = viewModel::onSubmit,
    )
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                CompleteAccountOneTimeEvents.NavigateToHome -> navigateToHome()
                CompleteAccountOneTimeEvents.NavigateToAuth -> navigateToAuth()
            }
        }
    }
}

@Composable
private fun CompleteAccountScreen(
    viewState: CompleteAccountViewState,
    onNavigateBack: () -> Unit,
    onShopNameChanged: (String) -> Unit,
    onCurrencyChanged: (Currency) -> Unit,
    onSubmit: () -> Unit,
) {
    val focusManager = LocalFocusManager.current
    Scaffold(
        modifier = Modifier.fillMaxSize().safeDrawingPadding(),
        topBar = {
            BcTopAppBar(
                onNavigationIconClicked = onNavigateBack,
                title = stringResource(Res.string.complete_account_title)
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(vertical = MaterialTheme.dimension.big)
                .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.big)
            ) {
                Text(
                    text = stringResource(Res.string.complete_account_description),
                    style = MaterialTheme.typography.titleMedium,
                )
                BcTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = viewState.shopName,
                    onValueChanged = onShopNameChanged,
                    title = Res.string.complete_account_shop_name_label,
                    placeholder = Res.string.complete_account_shop_name_placeholder,
                    error = viewState.shopNameError?.let { stringResource(it) }
                )
                var expanded by remember { mutableStateOf(false) }
                BcTextField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusable(false)
                        .clickable {
                            expanded = true
                            focusManager.clearFocus()
                        },
                    value = viewState.currency.name,
                    enabled = false,
                    readOnly = true,
                    onValueChanged = { },
                    title = Res.string.complete_account_currency_label,
                    error = viewState.currencyError?.let { stringResource(it) },
                    trailingIcon = {
                        IconButton(
                            onClick = {
                                expanded = !expanded
                                focusManager.clearFocus()
                            }
                        ) {
                            Icon(
                                imageVector = if (expanded) {
                                    Icons.Filled.ArrowDropUp
                                } else Icons.Filled.ArrowDropDown,
                                contentDescription = null
                            )
                        }
                        DropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            viewState.currencies.forEach {
                                DropdownMenuItem(
                                    text = { Text(text = it.name) },
                                    onClick = {
                                        onCurrencyChanged(it)
                                        expanded = false
                                    }
                                )
                            }
                        }
                    },
                    colors = bcReadOnlyTextFieldColors
                )
            }
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(Res.string.common_can_be_changed_later),
                    style = MaterialTheme.typography.labelMedium,
                )
                BcPrimaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.common_continue),
                    onClick = {
                        focusManager.clearFocus()
                        onSubmit()
                    },
                )
            }
        }
    }
}

@Preview
@Composable
private fun CompleteAccountScreenPreview() {
    BarbckerTheme {
        CompleteAccountScreen(
            viewState = CompleteAccountViewState(
                shopName = "My Shop",
                currency = Currency.EURO
            ),
            onNavigateBack = {},
            onShopNameChanged = {},
            onCurrencyChanged = {},
            onSubmit = {}
        )
    }
}