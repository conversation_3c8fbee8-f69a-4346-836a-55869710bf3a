package com.zrcoding.barbcker.data.datastore

import androidx.datastore.core.DataStore
import androidx.datastore.core.DataStoreFactory
import androidx.datastore.core.okio.OkioStorage
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import okio.FileSystem
import okio.Path.Companion.toPath
import org.koin.core.module.Module
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.proto.AccountSerializer


internal const val DATA_STORE_FILE_NAME_PREFERENCES = "dice.preferences_pb"
internal const val DATA_STORE_FILE_NAME_ACCOUNT = "account_proto_datastore.pb"

/**
 * Creates a [DataStore] instance for storing key-value pairs using [Preferences].
 *
 * @param producePath A lambda that produces the path where the DataStore should store its data.
 * @return A [DataStore] instance configured to store [Preferences].
 */
fun createDataStore(producePath: () -> String): DataStore<Preferences> {
    return PreferenceDataStoreFactory.createWithPath(produceFile = { producePath().toPath() })
}

/**
 * Creates a [DataStore] instance for storing protocol buffer data using [PbAccount].
 *
 * @param fileSystem The [FileSystem] used for reading and writing data.
 * @param producePath A lambda that produces the path where the DataStore should store its data.
 * @return A [DataStore] instance configured to store [PbAccount] data.
 */
fun createDataStore(
    fileSystem: FileSystem,
    producePath: () -> String
): DataStore<PbAccount> {
    return DataStoreFactory.create(
        storage = OkioStorage(
            fileSystem = fileSystem,
            producePath = { producePath().toPath() },
            serializer = AccountSerializer,
        ),
        migrations = listOf(),
    )
}

expect val datastoreModule: Module