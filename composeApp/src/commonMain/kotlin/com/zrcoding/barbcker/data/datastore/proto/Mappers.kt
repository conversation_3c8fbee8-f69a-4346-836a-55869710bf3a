package com.zrcoding.barbcker.data.datastore.proto

import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.PbConnectedAccount

fun Entrant.toPbAccount(shopName: String?, currency: Currency?): PbAccount {
    return PbAccount(
        notConnected = null,
        connected = PbConnectedAccount(
            accountId = uid,
            name = displayName,
            email = email,
            phone = phoneNumber,
            photoUrl = photoUrl,
            shopName = shopName,
            currency = currency?.name
        )
    )
}

fun PbAccount.toAccount(): Account {
    return if (connected == null) {
        Account.NotConnected
    } else {
        Account.Connected(
            uid = connected.accountId,
            email = connected.email,
            displayName = connected.name,
            phoneNumber = connected.phone,
            photoUrl = connected.photoUrl,
            shopName = connected.shopName,
            currency = connected.currency?.let { Currency.valueOf(it) }
        )
    }
}