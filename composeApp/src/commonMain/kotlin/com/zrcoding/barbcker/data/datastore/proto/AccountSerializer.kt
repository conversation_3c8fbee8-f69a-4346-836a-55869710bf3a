package se.scmv.morocco.datastore.proto

import androidx.datastore.core.IOException
import androidx.datastore.core.okio.OkioSerializer
import okio.BufferedSink
import okio.BufferedSource
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.PbNotConnectedAccount

object AccountSerializer : OkioSerializer<PbAccount> {
    override val defaultValue: PbAccount
        get() = PbAccount(notConnected = PbNotConnectedAccount(), connected = null)

    override suspend fun readFrom(source: BufferedSource): PbAccount {
        try {
            return PbAccount.ADAPTER.decode(source)
        } catch (exception: IOException) {
            throw Exception(exception.message ?: "Serialization Exception")
        }
    }

    override suspend fun writeTo(t: PbAccount, sink: BufferedSink) {
        sink.write(t.encode())
    }
}