package com.zrcoding.barbcker.data.repositories_impl

import androidx.datastore.core.DataStore
import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.data.datastore.proto.toAccount
import com.zrcoding.barbcker.data.datastore.proto.toPbAccount
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import se.scmv.morocco.datastore.PbAccount

class AccountRepositoryImpl(
    private val accountDataStore: DataStore<PbAccount>,
) : AccountRepository {

    override suspend fun saveAccount(entrant: Entrant, shopName: String?, currency: Currency?) {
        accountDataStore.updateData {
            entrant.toPbAccount(shopName, currency)
        }
    }

    override suspend fun editAccount(shopName: String, currency: Currency) {
        accountDataStore.updateData {
            it.copy(
                notConnected = null,
                connected = it.connected?.copy(shopName = shopName, currency = currency.name)
            )
        }
    }

    override suspend fun getAccount(): Flow<Account> {
        return accountDataStore.data.map { it.toAccount() }
    }
}