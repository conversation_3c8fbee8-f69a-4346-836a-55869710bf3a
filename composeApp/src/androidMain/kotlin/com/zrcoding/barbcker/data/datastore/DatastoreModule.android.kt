package com.zrcoding.barbcker.data.datastore

import android.content.Context
import com.zrcoding.barbcker.data.datastore.prefs.PreferencesDataSource
import okio.FileSystem
import org.koin.core.module.Module
import org.koin.dsl.module

actual val datastoreModule: Module = module {
    single {
        val context: Context = get()
        PreferencesDataSource(
            dataStore = createDataStore {
                context.filesDir.resolve(DATA_STORE_FILE_NAME_PREFERENCES).absolutePath
            }
        )
    }
    single {
        val context: Context = get()
        createDataStore(
            fileSystem = FileSystem.SYSTEM,
            producePath = { context.filesDir.resolve(DATA_STORE_FILE_NAME_ACCOUNT).absolutePath }
        )
    }
}